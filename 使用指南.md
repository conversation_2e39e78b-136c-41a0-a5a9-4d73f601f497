# PCAP生成工具 - 使用指南

## 🚀 快速开始

### 1. 环境准备
确保您的系统已安装Python 3.7或更高版本：
```bash
python --version
```

### 2. 安装依赖
在项目目录下运行：
```bash
pip install scapy
```
或者：
```bash
pip install -r requirements.txt
```

### 3. 验证安装
运行快速测试：
```bash
python quick_test.py
```

### 4. 启动程序
```bash
python main.py
```
或者双击 `run.bat` 文件

## 📋 界面使用说明

### 以太网配置
- **源MAC地址**: 发送方MAC地址，格式如 `00:11:22:33:44:55`
- **目标MAC地址**: 接收方MAC地址，支持冒号或短横线分隔

### IP层配置
- **IP版本**: 选择IPv4或IPv6
- **源IP地址**: 发送方IP地址
- **目标IP地址**: 接收方IP地址
- **IPv4参数**: TTL（生存时间）、TOS（服务类型）
- **IPv6参数**: Hop Limit（跳数限制）、Traffic Class（流量类别）

### 传输层配置
- **协议**: 选择TCP或UDP
- **源端口**: 发送方端口（1-65535）
- **目标端口**: 接收方端口（1-65535）

#### TCP配置
- **数据配置模式**:
  - **简单模式**: 填写C2S和S2C数据内容，支持UTF-8文本和转义序列
  - **高级模式**: 自定义每一帧的内容、方向和格式

#### UDP配置（新功能）
- **数据配置模式**:
  - **简单模式**: 填写C2S和S2C数据内容，支持UTF-8文本和转义序列
  - **高级模式**: 自定义每一帧的内容、方向和格式（支持十六进制）

### 简单模式配置（更新）
- **数据大小**: 单个数据帧的最大字节数（用于分片）
- **C2S数据框**: 客户端→服务器的UTF-8数据内容
- **S2C数据框**: 服务器→客户端的UTF-8数据内容
- **转义序列支持**: `\\r\\n`（回车换行）、`\\t`（制表符）等

### 数据包预览功能（新功能）
- **预览列表**: 实时显示生成的数据包信息
- **列表内容**: 序号、协议、源/目标地址、长度、摘要信息
- **协议分析**: 自动识别TCP/UDP协议和标志位
- **详细信息**: 双击数据包查看完整的协议层分析和十六进制转储
- **自动更新**: 生成数据包后自动刷新预览列表

### 高级模式配置（新功能）
- **最大帧大小**: 单个数据帧的最大字节数（默认1460）
- **数据帧列表**:
  - **方向**: 选择"客户端→服务器"或"服务器→客户端"
  - **数据内容**: 填写要发送的数据
  - **格式**: 选择"UTF-8"或"十六进制"
  - **操作**: 添加、删除数据帧

## 🔧 操作步骤

### 生成数据包模式

1. **配置网络参数**
   - 填写所有必要的网络配置
   - 程序会自动验证输入格式

2. **生成数据包**
   - 点击"生成数据包"按钮
   - 等待状态栏显示生成结果

3. **预览数据包**
   - 生成完成后自动显示数据包预览列表
   - 可以点击"预览数据包"按钮刷新列表
   - 双击任意数据包查看详细信息

4. **保存PCAP文件**
   - 点击"保存PCAP文件"按钮
   - 选择保存位置和文件名

### PCAP编辑模式（新功能）

1. **读取PCAP文件**
   - 点击"读取PCAP文件"按钮
   - 选择要编辑的PCAP或PCAPNG文件
   - 文件读取后自动显示数据包列表

2. **编辑数据包**
   - 点击"编辑数据包"按钮打开编辑器
   - 在左侧列表中选择要编辑的数据包
   - 在右侧编辑各层的字段值

3. **分层编辑**
   - **以太网层**: 修改源/目标MAC地址、类型
   - **IP层**: 修改IP地址、TTL、协议等字段
   - **传输层**: 修改端口、序列号、标志位等
   - **应用层**: 编辑数据内容（支持十六进制、ASCII、UTF-8）

4. **应用修改**
   - 点击"应用修改"保存当前数据包的修改
   - 点击"重置"恢复原始值
   - 修改后可在主界面预览效果

## 📝 配置示例

### 简单模式示例

#### HTTP请求响应（简单模式）
```
源MAC: 00:11:22:33:44:55
目标MAC: AA:BB:CC:DD:EE:FF
IP版本: IPv4
源IP: ***********00
目标IP: *************
协议: TCP
源端口: 12345
目标端口: 80
数据配置模式: 简单模式
数据大小: 1460

C2S数据框: GET /index.html HTTP/1.1\r\nHost: example.com\r\n\r\n
S2C数据框: HTTP/1.1 200 OK\r\nContent-Length: 13\r\n\r\nHello World!
```

#### API调用模拟（简单模式）
```
C2S数据框: POST /api/login HTTP/1.1\r\nContent-Type: application/json\r\n\r\n{"username":"admin","password":"123456"}
S2C数据框: HTTP/1.1 200 OK\r\nContent-Type: application/json\r\n\r\n{"token":"abc123","status":"success"}
```

### TCP连接（传统方式）示例
```
源MAC: 00:11:22:33:44:55
目标MAC: AA:BB:CC:DD:EE:FF
IP版本: IPv4
源IP: ***********00
目标IP: *************
TTL: 64
TOS: 0
协议: TCP
源端口: 12345
目标端口: 80
数据大小: 2920
```
这将生成：
- SYN包（连接请求）
- SYN-ACK包（连接响应）
- ACK包（连接确认）
- 2个数据包（每个1460字节）
- 对应的ACK响应包

### UDP包示例（简单模式）
```
源MAC: 00:11:22:33:44:55
目标MAC: AA:BB:CC:DD:EE:FF
IP版本: IPv4
源IP: ***********00
目标IP: *******
协议: UDP
源端口: 12345
目标端口: 53
数据配置模式: 简单模式

C2S数据框: DNS Query: www.example.com A?
S2C数据框: DNS Response: www.example.com A *************
```

### UDP包示例（高级模式）
```
协议: UDP
数据配置模式: 高级模式

数据帧1:
- 方向: 客户端→服务器
- 数据内容: DHCP Discover
- 格式: UTF-8

数据帧2:
- 方向: 服务器→客户端
- 数据内容: 01:02:03:04:05:06
- 格式: 十六进制
```

### 高级模式示例

#### HTTP请求响应模拟
```
数据配置模式: 高级模式
最大帧大小: 1460

数据帧1:
- 方向: 客户端→服务器
- 数据内容: GET /index.html HTTP/1.1\r\nHost: example.com\r\n\r\n
- 格式: UTF-8

数据帧2:
- 方向: 服务器→客户端
- 数据内容: HTTP/1.1 200 OK\r\nContent-Length: 13\r\n\r\nHello World!
- 格式: UTF-8
```

#### 十六进制数据传输
```
数据帧1:
- 方向: 客户端→服务器
- 数据内容: 48656C6C6F20576F726C64
- 格式: 十六进制
- 说明: 对应"Hello World"的十六进制

数据帧2:
- 方向: 服务器→客户端
- 数据内容: 48:54:54:50:2F:31:2E:31:20:32:30:30:20:4F:4B
- 格式: 十六进制
- 说明: 对应"HTTP/1.1 200 OK"的十六进制
```

#### 大数据自动分片示例
```
数据帧1:
- 方向: 客户端→服务器
- 数据内容: [超过1460字节的长文本]
- 格式: UTF-8
- 结果: 自动分成多个TCP包，每个包最大1460字节，并生成对应的ACK响应
```

## 🛠️ 故障排除

### 常见问题

**Q: 提示"No module named 'scapy'"**
A: 运行 `pip install scapy` 安装scapy库

**Q: MAC地址格式错误**
A: 使用格式 `XX:XX:XX:XX:XX:XX` 或 `XX-XX-XX-XX-XX-XX`

**Q: IP地址无效**
A: 检查IPv4格式（如***********）或IPv6格式（如2001:db8::1）

**Q: 端口号无效**
A: 端口范围必须在1-65535之间

**Q: 生成的PCAP文件无法打开**
A: 确保文件完整保存，可用Wireshark等工具验证

**Q: 高级模式下十六进制数据格式错误**
A: 支持格式：`48656C6C6F`、`48:65:6C:6C:6F`、`48-65-6C-6C-6F`、`48 65 6C 6C 6F`

**Q: 数据帧自动分片不正确**
A: 检查最大帧大小设置，确保大于0且小于等于1460字节

**Q: TCP连接中ACK包顺序混乱**
A: 这是正常的，工具会根据数据方向自动生成正确的序列号和ACK号

**Q: UTF-8中文字符显示异常**
A: 确保输入的中文字符编码正确，工具会自动转换为UTF-8字节序列

**Q: 数据包详细信息显示"hexdump"错误**
A: 已修复hexdump显示问题，现在使用备用方法确保总能显示数据包信息

**Q: 应用层数据格式切换后内容为空**
A: 已修复格式切换问题，现在可以安全地在十六进制、UTF-8、ASCII间切换而不丢失数据

**Q: 十六进制数据输入奇数长度**
A: 工具会自动在前面补零，例如"ABC"会被处理为"0ABC"

### 环境问题

**如果遇到conda环境问题：**
1. 打开命令提示符（不是PowerShell）
2. 直接运行：`python main.py`
3. 或者使用提供的 `run.bat` 文件

**如果Python版本过低：**
- Windows 7最高支持Python 3.8
- 建议使用Python 3.7或3.8

## 📦 打包部署

### 创建独立exe文件
```bash
python build.py
```
或运行：
```bash
build.bat
```

生成的exe文件位于 `dist/PcapGenerator.exe`，可在任何Windows 7+系统上运行。

## 🔍 验证输出

生成的PCAP文件可以用以下工具打开：
- **Wireshark**: 图形化网络分析工具
- **tcpdump**: 命令行抓包工具
- **tshark**: Wireshark命令行版本

### Wireshark验证步骤
1. 打开Wireshark
2. File → Open → 选择生成的.pcap文件
3. 查看数据包列表和详细信息
4. 验证MAC地址、IP地址、端口等信息

## 📞 技术支持

如果遇到问题：
1. 首先运行 `python quick_test.py` 检查环境
2. 检查Python版本是否为3.7+
3. 确认scapy库已正确安装
4. 查看错误信息并对照故障排除部分

## 🎯 使用场景

- **网络测试**: 生成特定的网络流量进行测试
- **协议分析**: 创建标准的网络包用于分析
- **教学演示**: 展示网络协议的工作原理
- **安全测试**: 生成测试数据包进行安全验证

工具已完全满足Windows 7兼容性要求，提供完整的图形界面和网络包生成功能！
