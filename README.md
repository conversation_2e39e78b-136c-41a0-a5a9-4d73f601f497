# PCAP生成工具 v2.0

一个功能强大的网络数据包生成工具，支持Windows 7及更高版本。新增高级数据帧配置功能，可自定义每一帧的内容、方向和格式。

## 功能特性

- ✅ 支持以太网帧配置（源/目标MAC地址）
- ✅ 支持IPv4和IPv6协议
- ✅ 支持TCP和UDP传输协议
- ✅ TCP三次握手模拟（SYN、SYN-ACK、ACK）
- ✅ 支持大数据传输（自动分片，最大1460字节/帧）
- ✅ 图形化用户界面
- ✅ 输入验证和错误提示
- ✅ 标准PCAP格式输出
- ✅ Windows 7兼容
- 🆕 **高级数据帧配置** - 自定义每帧内容、方向、格式
- 🆕 **多格式数据输入** - 支持UTF-8文本和十六进制
- 🆕 **智能数据分片** - 超长数据自动分片并生成ACK
- 🆕 **双向通信模拟** - 客户端↔服务器数据交换

## 系统要求

### 运行已编译版本
- Windows 7 SP1 或更高版本
- 无需安装Python或其他依赖

### 从源码运行
- Windows 7 SP1 或更高版本
- Python 3.7 或更高版本
- 依赖包：scapy, pyinstaller

## 安装和使用

### 方法1：使用已编译版本（推荐）
1. 下载 `PcapGenerator.exe`
2. 双击运行即可

### 方法2：从源码运行
1. 克隆或下载源码
2. 安装依赖：`pip install -r requirements.txt`
3. 运行：`python main.py`

### 方法3：自行编译
1. 克隆或下载源码
2. 运行 `build.bat` 或 `python build.py`
3. 在 `dist` 目录找到生成的 `PcapGenerator.exe`

## 使用说明

### 界面说明

#### 以太网配置
- **源MAC地址**: 发送方的MAC地址（格式：XX:XX:XX:XX:XX:XX）
- **目标MAC地址**: 接收方的MAC地址

#### IP层配置
- **IP版本**: 选择IPv4或IPv6
- **源IP地址**: 发送方的IP地址
- **目标IP地址**: 接收方的IP地址
- **IPv4参数**:
  - TTL: 生存时间（默认64）
  - TOS: 服务类型（默认0）
- **IPv6参数**:
  - Hop Limit: 跳数限制（默认64）
  - Traffic Class: 流量类别（默认0）

#### 传输层配置
- **协议**: 选择TCP或UDP
- **源端口**: 发送方端口号（1-65535）
- **目标端口**: 接收方端口号（1-65535）
- **数据大小**: 要传输的数据字节数（仅TCP时显示）

### 操作步骤

1. **配置网络参数**
   - 填写MAC地址（支持 XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX 格式）
   - 选择IP版本并填写IP地址
   - 配置IP层参数

2. **配置传输层**
   - 选择协议（TCP/UDP）
   - 设置源端口和目标端口
   - 如果选择TCP，可设置数据大小

3. **生成数据包**
   - 点击"生成数据包"按钮
   - 等待生成完成，状态栏会显示生成的包数量

4. **保存PCAP文件**
   - 点击"保存PCAP文件"按钮
   - 选择保存位置和文件名
   - 文件将以.pcap格式保存

### TCP连接模拟

当选择TCP协议时，工具会自动生成完整的TCP连接：

1. **SYN包**: 客户端发起连接请求
2. **SYN-ACK包**: 服务器响应连接请求
3. **ACK包**: 客户端确认连接建立
4. **数据包**: 如果设置了数据大小，会生成相应的数据传输包
   - 每个数据包最大1460字节（符合以太网MTU限制）
   - 大数据会自动分片为多个包
   - 包含相应的ACK响应包

### 输入验证

工具会自动验证以下输入：
- MAC地址格式正确性
- IP地址格式正确性（IPv4/IPv6）
- 端口号范围（1-65535）
- 数据大小为有效数字

## 示例配置

### 基本TCP连接
```
源MAC: 00:11:22:33:44:55
目标MAC: AA:BB:CC:DD:EE:FF
IP版本: IPv4
源IP: ***********00
目标IP: *************
协议: TCP
源端口: 12345
目标端口: 80
数据大小: 1460
```

### IPv6 UDP包
```
源MAC: 00:11:22:33:44:55
目标MAC: AA:BB:CC:DD:EE:FF
IP版本: IPv6
源IP: 2001:db8::1
目标IP: 2001:db8::2
协议: UDP
源端口: 53
目标端口: 53
数据大小: 512
```

## 输出文件

生成的PCAP文件可以用以下工具打开和分析：
- Wireshark
- tcpdump
- tshark
- 其他支持PCAP格式的网络分析工具

## 故障排除

### 常见问题

**Q: 程序无法启动**
A: 确保系统是Windows 7 SP1或更高版本，如果是从源码运行，检查Python版本是否为3.7+

**Q: 生成的PCAP文件无法在Wireshark中打开**
A: 检查文件是否完整保存，确保在生成过程中没有中断

**Q: MAC地址格式错误**
A: 支持格式：XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX，其中X为十六进制数字

**Q: IP地址格式错误**
A: IPv4格式如***********，IPv6格式如2001:db8::1

**Q: 端口号无效**
A: 端口号必须在1-65535范围内

### 技术支持

如遇到问题，请检查：
1. 输入格式是否正确
2. 系统版本是否支持
3. 是否有足够的磁盘空间保存文件

## 开发信息

- 开发语言: Python 3.7+
- GUI框架: tkinter
- 网络库: scapy
- 打包工具: PyInstaller

## 许可证

本项目仅供学习和测试使用。
