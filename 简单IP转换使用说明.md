# 简单IP转换功能使用说明

## 🎯 功能概述

简单IP转换功能可以将所有IPv4数据包转换为IPv6格式，适用于网络协议测试和数据包格式转换。

## 🚀 使用步骤

### 1. 生成IPv4数据包
- 启动程序 `python gui/main_window.py`
- 在主界面配置网络参数（MAC地址、IPv4地址、端口等）
- 点击"生成数据包"按钮生成一些IPv4数据包

### 2. 打开编辑器窗口
- 在主界面的数据包预览列表中，双击任意一个数据包
- 或者选中数据包后点击"编辑数据包"按钮
- 编辑器窗口将打开

### 3. 启动IP转换功能
- 在编辑器窗口底部，点击"简单IP转换"按钮
- IP转换对话框将打开

### 4. 配置转换参数
- **源IPv6地址**: 设置转换后的源IPv6地址（默认：2001:db8::1）
- **目标IPv6地址**: 设置转换后的目标IPv6地址（默认：2001:db8::2）
- 可以根据需要修改这些地址

### 5. 执行转换
- 点击"✓ 确定转换"按钮开始转换
- 转换状态区域会显示实时进度和结果
- 转换完成后会显示统计信息

### 6. 保存结果（可选）
- 点击"💾 保存文件"按钮
- 选择保存位置和文件名
- 转换后的数据包将保存为PCAP文件

### 7. 关闭对话框
- 点击"✕ 关闭"按钮关闭转换对话框
- 返回编辑器窗口

## 📋 界面说明

### 对话框布局
- **标题区域**: 显示功能名称和说明
- **IPv6地址设置**: 配置转换后的源和目标IPv6地址
- **转换状态**: 显示转换过程和结果的详细信息
- **按钮区域**: 三个操作按钮平均分布

### 按钮功能
- **✓ 确定转换**: 执行IPv4到IPv6的转换操作
- **💾 保存文件**: 将转换后的数据包保存到PCAP文件
- **✕ 关闭**: 关闭转换对话框

## 🔧 技术特性

### 分辨率适应
- 对话框会根据屏幕分辨率自动调整大小
- 最小尺寸：500x400像素
- 支持窗口大小调整
- 自动居中显示

### 转换过程
1. **检测IPv4数据包**: 自动识别需要转换的IPv4数据包
2. **保留以太网层**: 保持原始MAC地址，更新EtherType为IPv6
3. **创建IPv6层**: 使用指定的IPv6地址替换IPv4层
4. **保留传输层**: 保持原始的TCP/UDP层和数据内容
5. **更新校验和**: 自动重新计算相关校验和

### 状态显示
- 实时显示转换进度
- 显示转换成功的数据包数量
- 显示最终的IPv4/IPv6数据包统计
- 错误信息和警告提示

## ⚠ 注意事项

1. **数据包要求**: 只能转换包含IPv4层的数据包
2. **地址格式**: IPv6地址必须是有效的格式（如：2001:db8::1）
3. **内存使用**: 转换过程会在内存中创建新的数据包对象
4. **原始数据**: 转换操作会替换原始数据包列表
5. **保存建议**: 转换前建议先保存原始数据包

## 🎉 转换效果

转换前：
```
Ether / IP / TCP ***********:8080 > ***********:80
```

转换后：
```
Ether / IPv6 / TCP 2001:db8::1:8080 > 2001:db8::2:80
```

## 🔍 故障排除

### 常见问题
1. **对话框显示不完整**: 调整窗口大小或检查屏幕分辨率
2. **转换失败**: 检查是否有IPv4数据包，确认IPv6地址格式正确
3. **保存失败**: 检查文件路径权限，确保有足够的磁盘空间

### 解决方案
- 确保程序有足够的内存
- 检查输入的IPv6地址格式
- 尝试重新启动程序
