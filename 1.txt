(py37) PS E:\Src\pcapCreate> python.exe .\build.py > 1.txt

PCAP生成工具构建脚本
========================================
检查依赖项...
✓ scapy
✓ pyinstaller
开始构建PCAP生成工具...
清理之前的构建文件...
已删除目录: build
已删除目录: dist
已删除文件: PcapGenerator.spec
警告: 未找到icon.ico文件，将使用默认图标
执行PyInstaller命令...
命令: pyinstaller --onefile --windowed --name=PcapGenerator --add-data=gui;gui --add-data=core;core --add-data=utils;utils --hidden-import=scapy --hidden-import=scapy.all --hidden-import=scapy.layers --hidden-import=scapy.layers.inet --hidden-import=scapy.layers.inet6 --hidden-import=scapy.layers.l2 --hidden-import=scapy.packet --hidden-import=scapy.fields --hidden-import=scapy.base_classes --hidden-import=tkinter --hidden-import=tkinter.ttk --hidden-import=tkinter.messagebox --hidden-import=tkinter.filedialog --clean main.py
构建失败，退出代码: 1
请检查上面的错误信息
565 INFO: PyInstaller: 5.13.2
565 INFO: Python: 3.7.16 (conda)
565 INFO: Platform: Windows-10-10.0.26100-SP0
566 INFO: wrote E:\Src\pcapCreate\PcapGenerator.spec
567 INFO: UPX is not available.
567 INFO: Removing temporary files and cleaning cache in C:\Users\<USER>\AppData\Local\pyinstaller
569 INFO: Extending PYTHONPATH with paths
['E:\\Src\\pcapCreate']
890 INFO: checking Analysis
891 INFO: Building Analysis because Analysis-00.toc is non existent
891 INFO: Initializing module dependency graph...
892 INFO: Caching module graph hooks...
903 INFO: Analyzing base_library.zip ...
3407 INFO: Loading module hook 'hook-heapq.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
3491 INFO: Loading module hook 'hook-encodings.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
4498 INFO: Loading module hook 'hook-pickle.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
5954 INFO: Caching module dependency graph...
6093 INFO: running Analysis Analysis-00.toc
6111 INFO: Adding Microsoft.Windows.Common-Controls to dependent assemblies of final executable
  required by D:\Program Files\miniconda3\envs\py37\python.exe
6321 INFO: Analyzing E:\Src\pcapCreate\main.py
6773 INFO: Loading module hook 'hook-platform.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
7540 INFO: Loading module hook 'hook-difflib.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
7676 INFO: Loading module hook 'hook-matplotlib.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
7923 INFO: Loading module hook 'hook-numpy.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
8477 INFO: Loading module hook 'hook-numpy._pytesttester.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
8960 INFO: Loading module hook 'hook-sysconfig.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
10093 INFO: Loading module hook 'hook-packaging.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
10223 INFO: Processing pre-safe import module hook gi from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module\\hook-gi.py'.
10324 INFO: Loading module hook 'hook-PIL.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
10376 INFO: Loading module hook 'hook-PIL.Image.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks'...
11345 INFO: Loading module hook 'hook-pycparser.py' from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks'...
11847 INFO: Processing pre-safe import module hook distutils from 'D:\\Program Files\\miniconda3\\envs\\py37\\lib\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module\\hook-distutils.py'.
Traceback (most recent call last):
  File "D:\Program Files\miniconda3\envs\py37\Scripts\pyinstaller-script.py", line 10, in <module>
    sys.exit(run())
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\__main__.py", line 179, in run
    run_build(pyi_config, spec_file, **vars(args))
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\__main__.py", line 60, in run_build
    PyInstaller.building.build_main.main(pyi_config, spec_file, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\building\build_main.py", line 963, in main
    build(specfile, distpath, workpath, clean_build)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\building\build_main.py", line 885, in build
    exec(code, spec_namespace)
  File "E:\Src\pcapCreate\PcapGenerator.spec", line 20, in <module>
    noarchive=False,
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\building\build_main.py", line 410, in __init__
    self.__postinit__()
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\building\datastruct.py", line 173, in __postinit__
    self.assemble()
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\building\build_main.py", line 573, in assemble
    priority_scripts.append(self.graph.add_script(script))
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 268, in add_script
    self._top_script_node = super().add_script(pathname)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1433, in add_script
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1506, in import_hook
    source_package, target_module_partname, level)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1685, in _find_head_package
    target_module_headname, target_package_name, source_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1506, in import_hook
    source_package, target_module_partname, level)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1685, in _find_head_package
    target_module_headname, target_package_name, source_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2514, in _safe_import_hook
    edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1545, in import_hook
    target_module, target_attr_names):
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1795, in _import_importable_package_submodules
    attr_name, submodule_name, package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1518, in import_hook
    submodule = self._safe_import_module(head, mname, submodule)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1506, in import_hook
    source_package, target_module_partname, level)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1685, in _find_head_package
    target_module_headname, target_package_name, source_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2514, in _safe_import_hook
    edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1545, in import_hook
    target_module, target_attr_names):
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1795, in _import_importable_package_submodules
    attr_name, submodule_name, package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1506, in import_hook
    source_package, target_module_partname, level)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1685, in _find_head_package
    target_module_headname, target_package_name, source_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1506, in import_hook
    source_package, target_module_partname, level)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1685, in _find_head_package
    target_module_headname, target_package_name, source_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1506, in import_hook
    source_package, target_module_partname, level)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1685, in _find_head_package
    target_module_headname, target_package_name, source_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2514, in _safe_import_hook
    edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1545, in import_hook
    target_module, target_attr_names):
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1795, in _import_importable_package_submodules
    attr_name, submodule_name, package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 428, in _safe_import_module
    return super()._safe_import_module(module_basename, module_name, parent_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2062, in _safe_import_module
    self._process_imports(n)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2850, in _process_imports
    target_modules = self._safe_import_hook(*import_info, **kwargs)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 381, in _safe_import_hook
    return super()._safe_import_hook(target_module_partname, source_module, target_attr_names, level, edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 2303, in _safe_import_hook
    target_attr_names=None, level=level, edge_attr=edge_attr)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1506, in import_hook
    source_package, target_module_partname, level)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\lib\modulegraph\modulegraph.py", line 1685, in _find_head_package
    target_module_headname, target_package_name, source_package)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\depend\analysis.py", line 418, in _safe_import_module
    hook_module.pre_safe_import_module(hook_api)
  File "D:\Program Files\miniconda3\envs\py37\lib\site-packages\PyInstaller\hooks\pre_safe_import_module\hook-distutils.py", line 24, in pre_safe_import_module
    if compat.is_py312:
AttributeError: module 'PyInstaller.compat' has no attribute 'is_py312'
构建失败!
